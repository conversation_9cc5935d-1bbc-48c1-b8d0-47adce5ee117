import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Calendar,
  Heart,
  TrendingUp,
  Droplets,
  Moon,
  Sun,
  Activity,
  AlertCircle,
  CheckCircle,
  Clock,
  Target,
  Zap,
  Flower2,
  Plus,
  Settings,
  BookOpen,
  BarChart3
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { womensHealthService, CycleInsights, MenstrualCycle } from '../../lib/womensHealthService';
import { format, addDays, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday } from 'date-fns';

interface DashboardProps {}

const WomensHealthDashboard: React.FC<DashboardProps> = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [insights, setInsights] = useState<CycleInsights | null>(null);
  const [currentCycle, setCurrentCycle] = useState<MenstrualCycle | null>(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [activeTab, setActiveTab] = useState<'overview' | 'calendar' | 'insights' | 'settings'>('overview');

  useEffect(() => {
    if (user) {
      loadDashboardData();
    }
  }, [user]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [cycleInsights, cycle] = await Promise.all([
        womensHealthService.getCurrentCycleInsights(user!.id),
        womensHealthService.getCurrentCycle(user!.id)
      ]);

      setInsights(cycleInsights);
      setCurrentCycle(cycle);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'menstrual': return 'bg-red-100 text-red-800 border-red-200';
      case 'follicular': return 'bg-green-100 text-green-800 border-green-200';
      case 'ovulation': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'luteal': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPhaseIcon = (phase: string) => {
    switch (phase) {
      case 'menstrual': return <Droplets className="w-5 h-5" />;
      case 'follicular': return <Sun className="w-5 h-5" />;
      case 'ovulation': return <Flower2 className="w-5 h-5" />;
      case 'luteal': return <Moon className="w-5 h-5" />;
      default: return <Activity className="w-5 h-5" />;
    }
  };

  const getFertilityColor = (status: string) => {
    switch (status) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your health insights...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Women's Health</h1>
              <p className="text-gray-600 mt-1">Track your cycle, understand your body</p>
            </div>
            <div className="flex space-x-4">
              <button className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2">
                <Plus className="w-4 h-4" />
                <span>Log Today</span>
              </button>
              <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                <Settings className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex space-x-8 border-b border-gray-200">
            {[
              { id: 'overview', label: 'Overview', icon: BarChart3 },
              { id: 'calendar', label: 'Calendar', icon: Calendar },
              { id: 'insights', label: 'Insights', icon: TrendingUp },
              { id: 'settings', label: 'Settings', icon: Settings }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <AnimatePresence mode="wait">
          {activeTab === 'overview' && (
            <motion.div
              key="overview"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <OverviewTab insights={insights} currentCycle={currentCycle} />
            </motion.div>
          )}

          {activeTab === 'calendar' && (
            <motion.div
              key="calendar"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <CalendarTab selectedDate={selectedDate} setSelectedDate={setSelectedDate} />
            </motion.div>
          )}

          {activeTab === 'insights' && (
            <motion.div
              key="insights"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <InsightsTab insights={insights} />
            </motion.div>
          )}

          {activeTab === 'settings' && (
            <motion.div
              key="settings"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <SettingsTab />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

// =============================================
// OVERVIEW TAB COMPONENT
// =============================================

interface OverviewTabProps {
  insights: CycleInsights | null;
  currentCycle: MenstrualCycle | null;
}

const OverviewTab: React.FC<OverviewTabProps> = ({ insights, currentCycle }) => {
  if (!insights) {
    return (
      <div className="text-center py-12">
        <div className="bg-white rounded-xl p-8 shadow-sm">
          <Flower2 className="w-16 h-16 text-purple-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Welcome to Women's Health</h3>
          <p className="text-gray-600 mb-6">Start tracking your cycle to get personalized insights</p>
          <button className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
            Start Your First Cycle
          </button>
        </div>
      </div>
    );
  }

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'menstrual': return 'bg-red-100 text-red-800 border-red-200';
      case 'follicular': return 'bg-green-100 text-green-800 border-green-200';
      case 'ovulation': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'luteal': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPhaseIcon = (phase: string) => {
    switch (phase) {
      case 'menstrual': return <Droplets className="w-6 h-6" />;
      case 'follicular': return <Sun className="w-6 h-6" />;
      case 'ovulation': return <Flower2 className="w-6 h-6" />;
      case 'luteal': return <Moon className="w-6 h-6" />;
      default: return <Activity className="w-6 h-6" />;
    }
  };

  const getFertilityColor = (status: string) => {
    switch (status) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Current Phase Card */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Current Phase</h2>
          <div className={`px-3 py-1 rounded-full border text-sm font-medium ${getPhaseColor(insights.current_phase.phase)}`}>
            {insights.current_phase.phase.charAt(0).toUpperCase() + insights.current_phase.phase.slice(1)}
          </div>
        </div>

        <div className="flex items-start space-x-4">
          <div className={`p-3 rounded-lg ${getPhaseColor(insights.current_phase.phase)}`}>
            {getPhaseIcon(insights.current_phase.phase)}
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Day {insights.current_phase.day} - {insights.current_phase.description}
            </h3>
            <div className="space-y-2">
              {insights.current_phase.tips.slice(0, 3).map((tip, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-gray-600">{tip}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Next Period */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-red-100 rounded-lg">
              <Calendar className="w-5 h-5 text-red-600" />
            </div>
            <h3 className="font-medium text-gray-900">Next Period</h3>
          </div>
          <div className="text-2xl font-bold text-gray-900 mb-1">
            {insights.days_until_next_period !== undefined
              ? `${insights.days_until_next_period} days`
              : 'Calculating...'
            }
          </div>
          <p className="text-sm text-gray-600">Expected arrival</p>
        </div>

        {/* Fertility Status */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className={`p-2 rounded-lg ${getFertilityColor(insights.fertility_status)}`}>
              <Heart className="w-5 h-5" />
            </div>
            <h3 className="font-medium text-gray-900">Fertility</h3>
          </div>
          <div className="text-2xl font-bold text-gray-900 mb-1 capitalize">
            {insights.fertility_status}
          </div>
          <p className="text-sm text-gray-600">
            {insights.days_until_ovulation !== undefined
              ? `Ovulation in ${insights.days_until_ovulation} days`
              : 'Calculating...'
            }
          </p>
        </div>

        {/* Cycle Regularity */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <TrendingUp className="w-5 h-5 text-blue-600" />
            </div>
            <h3 className="font-medium text-gray-900">Cycle Pattern</h3>
          </div>
          <div className="text-2xl font-bold text-gray-900 mb-1 capitalize">
            {insights.cycle_regularity}
          </div>
          <p className="text-sm text-gray-600">
            Avg {insights.average_cycle_length} days
          </p>
        </div>

        {/* Symptom Score */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Activity className="w-5 h-5 text-purple-600" />
            </div>
            <h3 className="font-medium text-gray-900">Symptoms</h3>
          </div>
          <div className="text-2xl font-bold text-gray-900 mb-1">
            {Object.keys(insights.symptom_patterns).length}
          </div>
          <p className="text-sm text-gray-600">Tracked patterns</p>
        </div>
      </div>

      {/* Recommendations */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Personalized Recommendations</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {insights.recommendations.map((recommendation, index) => (
            <div key={index} className="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg">
              <div className="p-1 bg-purple-100 rounded">
                <Zap className="w-4 h-4 text-purple-600" />
              </div>
              <p className="text-sm text-gray-700">{recommendation}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="flex flex-col items-center space-y-2 p-4 bg-red-50 rounded-lg hover:bg-red-100 transition-colors">
            <Droplets className="w-6 h-6 text-red-600" />
            <span className="text-sm font-medium text-red-700">Log Period</span>
          </button>
          <button className="flex flex-col items-center space-y-2 p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
            <Activity className="w-6 h-6 text-purple-600" />
            <span className="text-sm font-medium text-purple-700">Log Symptoms</span>
          </button>
          <button className="flex flex-col items-center space-y-2 p-4 bg-pink-50 rounded-lg hover:bg-pink-100 transition-colors">
            <Heart className="w-6 h-6 text-pink-600" />
            <span className="text-sm font-medium text-pink-700">Fertility Data</span>
          </button>
          <button className="flex flex-col items-center space-y-2 p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
            <BookOpen className="w-6 h-6 text-blue-600" />
            <span className="text-sm font-medium text-blue-700">Learn More</span>
          </button>
        </div>
      </div>
    </div>
  );
};

// =============================================
// CALENDAR TAB COMPONENT
// =============================================

interface CalendarTabProps {
  selectedDate: Date;
  setSelectedDate: (date: Date) => void;
}

const CalendarTab: React.FC<CalendarTabProps> = ({ selectedDate, setSelectedDate }) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [periodLogs, setPeriodLogs] = useState<any[]>([]);
  const [symptomLogs, setSymptomLogs] = useState<any[]>([]);
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      loadCalendarData();
    }
  }, [user, currentMonth]);

  const loadCalendarData = async () => {
    try {
      const monthStart = startOfMonth(currentMonth);
      const monthEnd = endOfMonth(currentMonth);

      const [periods, symptoms] = await Promise.all([
        womensHealthService.getPeriodLogs(
          user!.id,
          monthStart.toISOString().split('T')[0],
          monthEnd.toISOString().split('T')[0]
        ),
        womensHealthService.getSymptomLogs(
          user!.id,
          monthStart.toISOString().split('T')[0],
          monthEnd.toISOString().split('T')[0]
        )
      ]);

      setPeriodLogs(periods);
      setSymptomLogs(symptoms);
    } catch (error) {
      console.error('Error loading calendar data:', error);
    }
  };

  const getDayData = (date: Date) => {
    const dateStr = format(date, 'yyyy-MM-dd');
    const periodLog = periodLogs.find(log => log.log_date === dateStr);
    const symptomLog = symptomLogs.find(log => log.log_date === dateStr);

    return { periodLog, symptomLog };
  };

  const getDayIndicators = (date: Date) => {
    const { periodLog, symptomLog } = getDayData(date);
    const indicators = [];

    if (periodLog?.has_period) {
      const intensity = periodLog.flow_intensity || 1;
      const color = intensity === 1 ? 'bg-red-300' : intensity === 2 ? 'bg-red-500' : 'bg-red-700';
      indicators.push(<div key="period" className={`w-2 h-2 rounded-full ${color}`} />);
    }

    if (symptomLog) {
      const hasSymptoms = Object.keys(symptomLog).some(key =>
        ['cramps', 'bloating', 'mood_swings', 'fatigue'].includes(key) && symptomLog[key]
      );
      if (hasSymptoms) {
        indicators.push(<div key="symptoms" className="w-2 h-2 rounded-full bg-purple-400" />);
      }
    }

    return indicators;
  };

  const monthDays = eachDayOfInterval({
    start: startOfMonth(currentMonth),
    end: endOfMonth(currentMonth)
  });

  const previousMonth = () => {
    setCurrentMonth(prev => new Date(prev.getFullYear(), prev.getMonth() - 1));
  };

  const nextMonth = () => {
    setCurrentMonth(prev => new Date(prev.getFullYear(), prev.getMonth() + 1));
  };

  return (
    <div className="space-y-6">
      {/* Calendar Header */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Cycle Calendar</h2>
          <div className="flex items-center space-x-4">
            <button
              onClick={previousMonth}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <h3 className="text-lg font-medium text-gray-900 min-w-[140px] text-center">
              {format(currentMonth, 'MMMM yyyy')}
            </h3>
            <button
              onClick={nextMonth}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1 mb-4">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="p-3 text-center text-sm font-medium text-gray-500">
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-1">
          {monthDays.map(date => {
            const indicators = getDayIndicators(date);
            const isSelected = isSameDay(date, selectedDate);
            const isCurrentDay = isToday(date);

            return (
              <button
                key={date.toISOString()}
                onClick={() => setSelectedDate(date)}
                className={`
                  p-3 text-sm rounded-lg transition-colors relative
                  ${isSelected
                    ? 'bg-purple-600 text-white'
                    : isCurrentDay
                      ? 'bg-purple-100 text-purple-700 font-medium'
                      : 'hover:bg-gray-100 text-gray-700'
                  }
                `}
              >
                <div className="flex flex-col items-center space-y-1">
                  <span>{format(date, 'd')}</span>
                  {indicators.length > 0 && (
                    <div className="flex space-x-1">
                      {indicators}
                    </div>
                  )}
                </div>
              </button>
            );
          })}
        </div>

        {/* Legend */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Legend</h4>
          <div className="flex flex-wrap gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <span className="text-gray-600">Period (Heavy)</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-red-300"></div>
              <span className="text-gray-600">Period (Light)</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-purple-400"></div>
              <span className="text-gray-600">Symptoms</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-purple-100 border border-purple-300"></div>
              <span className="text-gray-600">Today</span>
            </div>
          </div>
        </div>
      </div>

      {/* Selected Day Details */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {format(selectedDate, 'EEEE, MMMM d, yyyy')}
        </h3>

        <SelectedDayDetails date={selectedDate} />
      </div>
    </div>
  );
};

// =============================================
// SELECTED DAY DETAILS COMPONENT
// =============================================

interface SelectedDayDetailsProps {
  date: Date;
}

const SelectedDayDetails: React.FC<SelectedDayDetailsProps> = ({ date }) => {
  const { user } = useAuth();
  const [dayData, setDayData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadDayData();
    }
  }, [user, date]);

  const loadDayData = async () => {
    try {
      setLoading(true);
      const dateStr = format(date, 'yyyy-MM-dd');

      const [periodLogs, symptomLogs, fertilityLogs] = await Promise.all([
        womensHealthService.getPeriodLogs(user!.id, dateStr, dateStr),
        womensHealthService.getSymptomLogs(user!.id, dateStr, dateStr),
        womensHealthService.getFertilityLogs(user!.id, dateStr, dateStr)
      ]);

      setDayData({
        period: periodLogs[0] || null,
        symptoms: symptomLogs[0] || null,
        fertility: fertilityLogs[0] || null
      });
    } catch (error) {
      console.error('Error loading day data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>;
  }

  if (!dayData?.period && !dayData?.symptoms && !dayData?.fertility) {
    return (
      <div className="text-center py-8">
        <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-3" />
        <p className="text-gray-500 mb-4">No data logged for this day</p>
        <button className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
          Add Data
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Period Data */}
      {dayData.period && (
        <div className="border border-red-200 rounded-lg p-4 bg-red-50">
          <h4 className="font-medium text-red-800 mb-2 flex items-center">
            <Droplets className="w-4 h-4 mr-2" />
            Period
          </h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Flow: </span>
              <span className="font-medium">
                {dayData.period.flow_intensity === 1 ? 'Light' :
                 dayData.period.flow_intensity === 2 ? 'Normal' : 'Heavy'}
              </span>
            </div>
            {dayData.period.flow_color && (
              <div>
                <span className="text-gray-600">Color: </span>
                <span className="font-medium capitalize">
                  {dayData.period.flow_color.replace('_', ' ')}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Symptoms Data */}
      {dayData.symptoms && (
        <div className="border border-purple-200 rounded-lg p-4 bg-purple-50">
          <h4 className="font-medium text-purple-800 mb-2 flex items-center">
            <Activity className="w-4 h-4 mr-2" />
            Symptoms
          </h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            {Object.entries(dayData.symptoms).map(([key, value]) => {
              if (!value || ['id', 'user_id', 'log_date', 'created_at', 'updated_at', 'notes'].includes(key)) {
                return null;
              }
              return (
                <div key={key}>
                  <span className="text-gray-600 capitalize">{key.replace('_', ' ')}: </span>
                  <span className="font-medium">{value}/5</span>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Fertility Data */}
      {dayData.fertility && (
        <div className="border border-pink-200 rounded-lg p-4 bg-pink-50">
          <h4 className="font-medium text-pink-800 mb-2 flex items-center">
            <Heart className="w-4 h-4 mr-2" />
            Fertility
          </h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            {dayData.fertility.basal_body_temperature && (
              <div>
                <span className="text-gray-600">BBT: </span>
                <span className="font-medium">{dayData.fertility.basal_body_temperature}°C</span>
              </div>
            )}
            {dayData.fertility.cervical_mucus && (
              <div>
                <span className="text-gray-600">Cervical Mucus: </span>
                <span className="font-medium capitalize">
                  {dayData.fertility.cervical_mucus.replace('_', ' ')}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// =============================================
// INSIGHTS TAB COMPONENT
// =============================================

interface InsightsTabProps {
  insights: CycleInsights | null;
}

const InsightsTab: React.FC<InsightsTabProps> = ({ insights }) => {
  if (!insights) {
    return (
      <div className="text-center py-12">
        <TrendingUp className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">No Insights Available</h3>
        <p className="text-gray-600">Track your cycle for a few months to see detailed insights</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Symptom Patterns */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Symptom Patterns</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(insights.symptom_patterns).map(([symptom, average]) => (
            <div key={symptom} className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 capitalize mb-2">
                {symptom.replace('_', ' ')}
              </h3>
              <div className="flex items-center space-x-2">
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-purple-600 h-2 rounded-full"
                    style={{ width: `${(average / 5) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-700">
                  {average.toFixed(1)}/5
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Cycle Analysis */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Cycle Analysis</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">
              {insights.average_cycle_length}
            </div>
            <p className="text-gray-600">Average Cycle Length</p>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2 capitalize">
              {insights.cycle_regularity}
            </div>
            <p className="text-gray-600">Cycle Regularity</p>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-pink-600 mb-2 capitalize">
              {insights.fertility_status}
            </div>
            <p className="text-gray-600">Current Fertility</p>
          </div>
        </div>
      </div>
    </div>
  );
};

// =============================================
// SETTINGS TAB COMPONENT
// =============================================

const SettingsTab: React.FC = () => {
  const { user } = useAuth();
  const [settings, setSettings] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadSettings();
    }
  }, [user]);

  const loadSettings = async () => {
    try {
      const userSettings = await womensHealthService.getUserSettings(user!.id);
      setSettings(userSettings);
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateSetting = async (key: string, value: any) => {
    try {
      const updatedSettings = await womensHealthService.updateUserSettings(user!.id, {
        [key]: value
      });
      setSettings(updatedSettings);
    } catch (error) {
      console.error('Error updating settings:', error);
    }
  };

  if (loading) {
    return <div className="animate-pulse bg-gray-200 h-64 rounded-lg"></div>;
  }

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Cycle Settings</h2>

        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Average Cycle Length
              </label>
              <input
                type="number"
                min="21"
                max="45"
                value={settings?.average_cycle_length || 28}
                onChange={(e) => updateSetting('average_cycle_length', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <p className="text-xs text-gray-500 mt-1">Days (21-45)</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Average Period Length
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={settings?.average_period_length || 5}
                onChange={(e) => updateSetting('average_period_length', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <p className="text-xs text-gray-500 mt-1">Days (1-10)</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Luteal Phase Length
              </label>
              <input
                type="number"
                min="10"
                max="16"
                value={settings?.luteal_phase_length || 14}
                onChange={(e) => updateSetting('luteal_phase_length', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <p className="text-xs text-gray-500 mt-1">Days (10-16)</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Tracking Preferences</h2>

        <div className="space-y-4">
          {[
            { key: 'track_symptoms', label: 'Track Symptoms', description: 'Log daily physical and emotional symptoms' },
            { key: 'track_fertility', label: 'Track Fertility', description: 'Monitor fertility signs and ovulation' },
            { key: 'track_mood', label: 'Track Mood', description: 'Record mood changes throughout your cycle' },
            { key: 'period_reminders', label: 'Period Reminders', description: 'Get notified before your period starts' },
            { key: 'ovulation_reminders', label: 'Ovulation Reminders', description: 'Get notified during fertile window' }
          ].map((option) => (
            <div key={option.key} className="flex items-center justify-between py-3">
              <div>
                <h3 className="font-medium text-gray-900">{option.label}</h3>
                <p className="text-sm text-gray-600">{option.description}</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings?.[option.key] || false}
                  onChange={(e) => updateSetting(option.key, e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WomensHealthDashboard;