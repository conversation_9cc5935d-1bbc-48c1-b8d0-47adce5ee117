import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Activity,
  Brain,
  Heart,
  Zap,
  Moon,
  Sun,
  AlertCircle,
  TrendingUp,
  Calendar,
  Save,
  RotateCcw
} from 'lucide-react';
import { format, subDays, addDays } from 'date-fns';
import { useAuth } from '../../contexts/AuthContext';
import { womensHealthService, DailySymptoms } from '../../lib/womensHealthService';

interface SymptomTrackerProps {
  selectedDate?: Date;
  onDateChange?: (date: Date) => void;
  showDateNavigation?: boolean;
}

interface SymptomCategory {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  color: string;
  symptoms: SymptomItem[];
}

interface SymptomItem {
  key: string;
  label: string;
  description: string;
  maxValue: number;
  unit?: string;
}

const SymptomTracker: React.FC<SymptomTrackerProps> = ({
  selectedDate = new Date(),
  onDateChange,
  showDateNavigation = true
}) => {
  const { user } = useAuth();
  const [currentDate, setCurrentDate] = useState(selectedDate);
  const [symptoms, setSymptoms] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [notes, setNotes] = useState('');
  const [hasChanges, setHasChanges] = useState(false);

  const symptomCategories: SymptomCategory[] = [
    {
      id: 'physical',
      title: 'Physical Symptoms',
      icon: Activity,
      color: 'red',
      symptoms: [
        { key: 'cramps', label: 'Cramps', description: 'Menstrual cramps or pelvic pain', maxValue: 5 },
        { key: 'bloating', label: 'Bloating', description: 'Abdominal bloating or swelling', maxValue: 5 },
        { key: 'breast_tenderness', label: 'Breast Tenderness', description: 'Breast pain or sensitivity', maxValue: 5 },
        { key: 'headache', label: 'Headache', description: 'Head pain or tension', maxValue: 5 },
        { key: 'back_pain', label: 'Back Pain', description: 'Lower back pain or discomfort', maxValue: 5 },
        { key: 'fatigue', label: 'Fatigue', description: 'Tiredness or lack of energy', maxValue: 5 },
        { key: 'nausea', label: 'Nausea', description: 'Feeling sick or queasy', maxValue: 5 },
        { key: 'acne', label: 'Acne', description: 'Skin breakouts or blemishes', maxValue: 5 },
        { key: 'hot_flashes', label: 'Hot Flashes', description: 'Sudden feeling of heat', maxValue: 5 }
      ]
    },
    {
      id: 'emotional',
      title: 'Emotional Symptoms',
      icon: Brain,
      color: 'purple',
      symptoms: [
        { key: 'mood_swings', label: 'Mood Swings', description: 'Rapid changes in mood', maxValue: 5 },
        { key: 'irritability', label: 'Irritability', description: 'Feeling easily annoyed or frustrated', maxValue: 5 },
        { key: 'anxiety', label: 'Anxiety', description: 'Feeling worried or nervous', maxValue: 5 },
        { key: 'depression', label: 'Depression', description: 'Feeling sad or down', maxValue: 5 },
        { key: 'emotional_sensitivity', label: 'Emotional Sensitivity', description: 'Feeling more emotional than usual', maxValue: 5 }
      ]
    },
    {
      id: 'overall',
      title: 'Overall Well-being',
      icon: Heart,
      color: 'blue',
      symptoms: [
        { key: 'energy_level', label: 'Energy Level', description: 'Overall energy and vitality', maxValue: 10, unit: '/10' },
        { key: 'mood_rating', label: 'Mood Rating', description: 'Overall mood and happiness', maxValue: 10, unit: '/10' },
        { key: 'sleep_quality', label: 'Sleep Quality', description: 'Quality of sleep last night', maxValue: 10, unit: '/10' }
      ]
    }
  ];

  useEffect(() => {
    if (user) {
      loadSymptomData();
    }
  }, [user, currentDate]);

  useEffect(() => {
    if (onDateChange) {
      onDateChange(currentDate);
    }
  }, [currentDate, onDateChange]);

  const loadSymptomData = async () => {
    try {
      setLoading(true);
      const dateStr = format(currentDate, 'yyyy-MM-dd');
      const symptomLogs = await womensHealthService.getSymptomLogs(user!.id, dateStr, dateStr);

      if (symptomLogs.length > 0) {
        const log = symptomLogs[0];
        const loadedSymptoms: Record<string, number> = {};

        // Load all symptom values
        symptomCategories.forEach(category => {
          category.symptoms.forEach(symptom => {
            loadedSymptoms[symptom.key] = log[symptom.key as keyof DailySymptoms] as number || 0;
          });
        });

        setSymptoms(loadedSymptoms);
        setNotes(log.notes || '');
      } else {
        // Reset to default values
        const defaultSymptoms: Record<string, number> = {};
        symptomCategories.forEach(category => {
          category.symptoms.forEach(symptom => {
            defaultSymptoms[symptom.key] = symptom.key === 'energy_level' || symptom.key === 'mood_rating' || symptom.key === 'sleep_quality' ? 5 : 0;
          });
        });
        setSymptoms(defaultSymptoms);
        setNotes('');
      }

      setHasChanges(false);
    } catch (error) {
      console.error('Error loading symptom data:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateSymptom = (key: string, value: number) => {
    setSymptoms(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = direction === 'prev' ? subDays(currentDate, 1) : addDays(currentDate, 1);
    setCurrentDate(newDate);
  };

  const resetSymptoms = () => {
    const defaultSymptoms: Record<string, number> = {};
    symptomCategories.forEach(category => {
      category.symptoms.forEach(symptom => {
        defaultSymptoms[symptom.key] = symptom.key === 'energy_level' || symptom.key === 'mood_rating' || symptom.key === 'sleep_quality' ? 5 : 0;
      });
    });
    setSymptoms(defaultSymptoms);
    setNotes('');
    setHasChanges(true);
  };

  const saveSymptoms = async () => {
    try {
      setSaving(true);
      const dateStr = format(currentDate, 'yyyy-MM-dd');

      // Only save symptoms that have values > 0 (except for overall ratings)
      const symptomsToSave: Record<string, number> = {};
      Object.entries(symptoms).forEach(([key, value]) => {
        if (value > 0 || ['energy_level', 'mood_rating', 'sleep_quality'].includes(key)) {
          symptomsToSave[key] = value;
        }
      });

      await womensHealthService.logSymptoms(user!.id, {
        log_date: dateStr,
        notes: notes.trim() || undefined,
        ...symptomsToSave
      });

      setHasChanges(false);
    } catch (error) {
      console.error('Error saving symptoms:', error);
    } finally {
      setSaving(false);
    }
  };

  const getIntensityColor = (value: number, maxValue: number, colorScheme: string) => {
    if (value === 0) return 'bg-gray-100';

    const intensity = value / maxValue;
    const colors = {
      red: ['bg-red-100', 'bg-red-200', 'bg-red-300', 'bg-red-400', 'bg-red-500'],
      purple: ['bg-purple-100', 'bg-purple-200', 'bg-purple-300', 'bg-purple-400', 'bg-purple-500'],
      blue: ['bg-blue-100', 'bg-blue-200', 'bg-blue-300', 'bg-blue-400', 'bg-blue-500']
    };

    const colorArray = colors[colorScheme as keyof typeof colors] || colors.red;
    const colorIndex = Math.min(Math.floor(intensity * colorArray.length), colorArray.length - 1);
    return colorArray[colorIndex];
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="space-y-3">
                <div className="h-6 bg-gray-200 rounded w-1/4"></div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Array.from({ length: 4 }).map((_, j) => (
                    <div key={j} className="h-20 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-pink-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Activity className="w-6 h-6 text-purple-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Symptom Tracker</h2>
              <p className="text-sm text-gray-600">Track your daily symptoms and well-being</p>
            </div>
          </div>

          {showDateNavigation && (
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigateDate('prev')}
                className="p-2 hover:bg-white hover:shadow-sm rounded-lg transition-all duration-200"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>

              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {format(currentDate, 'MMM d, yyyy')}
                </div>
                <div className="text-sm text-gray-600">
                  {format(currentDate, 'EEEE')}
                </div>
              </div>

              <button
                onClick={() => navigateDate('next')}
                className="p-2 hover:bg-white hover:shadow-sm rounded-lg transition-all duration-200"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          )}
        </div>
      </div>