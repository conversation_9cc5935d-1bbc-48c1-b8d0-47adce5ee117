import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  Droplets,
  Activity,
  Heart,
  Plus,
  Edit3,
  X
} from 'lucide-react';
import { format, addDays, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday, startOfWeek, endOfWeek } from 'date-fns';
import { useAuth } from '../../contexts/AuthContext';
import { womensHealthService, DailyPeriodLog, DailySymptoms, FertilityTracking } from '../../lib/womensHealthService';

interface MenstrualCalendarProps {
  onDateSelect?: (date: Date) => void;
  selectedDate?: Date;
  showQuickLog?: boolean;
}

interface CalendarDay {
  date: Date;
  periodLog?: DailyPeriodLog;
  symptoms?: DailySymptoms;
  fertility?: FertilityTracking;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
}

const MenstrualCalendar: React.FC<MenstrualCalendarProps> = ({
  onDateSelect,
  selectedDate,
  showQuickLog = true
}) => {
  const { user } = useAuth();
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [calendarData, setCalendarData] = useState<CalendarDay[]>([]);
  const [loading, setLoading] = useState(true);
  const [showQuickLogModal, setShowQuickLogModal] = useState(false);
  const [quickLogDate, setQuickLogDate] = useState<Date | null>(null);

  useEffect(() => {
    if (user) {
      loadCalendarData();
    }
  }, [user, currentMonth]);

  const loadCalendarData = async () => {
    try {
      setLoading(true);

      // Get the full month view including partial weeks
      const monthStart = startOfWeek(startOfMonth(currentMonth));
      const monthEnd = endOfWeek(endOfMonth(currentMonth));

      const [periodLogs, symptomLogs, fertilityLogs] = await Promise.all([
        womensHealthService.getPeriodLogs(
          user!.id,
          monthStart.toISOString().split('T')[0],
          monthEnd.toISOString().split('T')[0]
        ),
        womensHealthService.getSymptomLogs(
          user!.id,
          monthStart.toISOString().split('T')[0],
          monthEnd.toISOString().split('T')[0]
        ),
        womensHealthService.getFertilityLogs(
          user!.id,
          monthStart.toISOString().split('T')[0],
          monthEnd.toISOString().split('T')[0]
        )
      ]);

      // Create calendar days
      const days = eachDayOfInterval({ start: monthStart, end: monthEnd });
      const calendarDays: CalendarDay[] = days.map(date => {
        const dateStr = format(date, 'yyyy-MM-dd');
        const periodLog = periodLogs.find(log => log.log_date === dateStr);
        const symptoms = symptomLogs.find(log => log.log_date === dateStr);
        const fertility = fertilityLogs.find(log => log.log_date === dateStr);

        return {
          date,
          periodLog,
          symptoms,
          fertility,
          isCurrentMonth: date.getMonth() === currentMonth.getMonth(),
          isToday: isToday(date),
          isSelected: selectedDate ? isSameDay(date, selectedDate) : false
        };
      });

      setCalendarData(calendarDays);
    } catch (error) {
      console.error('Error loading calendar data:', error);
    } finally {
      setLoading(false);
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      newMonth.setMonth(prev.getMonth() + (direction === 'next' ? 1 : -1));
      return newMonth;
    });
  };

  const handleDateClick = (day: CalendarDay) => {
    if (onDateSelect) {
      onDateSelect(day.date);
    }

    if (showQuickLog && day.isCurrentMonth) {
      setQuickLogDate(day.date);
      setShowQuickLogModal(true);
    }
  };

  const getDayIndicators = (day: CalendarDay) => {
    const indicators = [];

    // Period indicator
    if (day.periodLog?.has_period) {
      const intensity = day.periodLog.flow_intensity || 1;
      const color = intensity === 1 ? 'bg-red-300' : intensity === 2 ? 'bg-red-500' : 'bg-red-700';
      indicators.push(
        <div key="period" className={`w-2 h-2 rounded-full ${color}`} title="Period" />
      );
    }

    // Symptoms indicator
    if (day.symptoms) {
      const hasSymptoms = Object.keys(day.symptoms).some(key =>
        ['cramps', 'bloating', 'mood_swings', 'fatigue', 'headache', 'breast_tenderness'].includes(key) &&
        day.symptoms![key as keyof DailySymptoms]
      );
      if (hasSymptoms) {
        indicators.push(
          <div key="symptoms" className="w-2 h-2 rounded-full bg-purple-400" title="Symptoms" />
        );
      }
    }

    // Fertility indicator
    if (day.fertility) {
      if (day.fertility.predicted_ovulation) {
        indicators.push(
          <div key="ovulation" className="w-2 h-2 rounded-full bg-pink-500" title="Ovulation" />
        );
      } else if (day.fertility.fertile_window) {
        indicators.push(
          <div key="fertile" className="w-2 h-2 rounded-full bg-pink-300" title="Fertile Window" />
        );
      }
    }

    return indicators;
  };

  const getDayClassName = (day: CalendarDay) => {
    let baseClass = "relative p-2 h-12 w-full text-sm transition-all duration-200 rounded-lg cursor-pointer ";

    if (!day.isCurrentMonth) {
      baseClass += "text-gray-400 hover:bg-gray-50 ";
    } else if (day.isSelected) {
      baseClass += "bg-purple-600 text-white shadow-lg ";
    } else if (day.isToday) {
      baseClass += "bg-purple-100 text-purple-700 font-semibold border-2 border-purple-300 ";
    } else {
      baseClass += "text-gray-700 hover:bg-gray-100 ";
    }

    return baseClass;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="grid grid-cols-7 gap-2">
            {Array.from({ length: 42 }).map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        {/* Calendar Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-pink-50">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <Calendar className="w-5 h-5 mr-2 text-purple-600" />
              {format(currentMonth, 'MMMM yyyy')}
            </h2>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => navigateMonth('prev')}
                className="p-2 hover:bg-white hover:shadow-sm rounded-lg transition-all duration-200"
              >
                <ChevronLeft className="w-5 h-5 text-gray-600" />
              </button>
              <button
                onClick={() => setCurrentMonth(new Date())}
                className="px-3 py-1 text-sm font-medium text-purple-600 hover:bg-white hover:shadow-sm rounded-lg transition-all duration-200"
              >
                Today
              </button>
              <button
                onClick={() => navigateMonth('next')}
                className="p-2 hover:bg-white hover:shadow-sm rounded-lg transition-all duration-200"
              >
                <ChevronRight className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </div>
        </div>

        {/* Calendar Grid */}
        <div className="p-6">
          {/* Day Headers */}
          <div className="grid grid-cols-7 gap-2 mb-4">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
              <div key={day} className="text-center text-sm font-medium text-gray-500 py-2">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Days */}
          <div className="grid grid-cols-7 gap-2">
            {calendarData.map((day, index) => {
              const indicators = getDayIndicators(day);

              return (
                <motion.button
                  key={`${day.date.toISOString()}-${index}`}
                  onClick={() => handleDateClick(day)}
                  className={getDayClassName(day)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ duration: 0.1 }}
                >
                  <div className="flex flex-col items-center justify-center h-full">
                    <span className="text-center">
                      {format(day.date, 'd')}
                    </span>
                    {indicators.length > 0 && (
                      <div className="flex space-x-1 mt-1">
                        {indicators.slice(0, 3)} {/* Limit to 3 indicators */}
                      </div>
                    )}
                  </div>
                </motion.button>
              );
            })}
          </div>
        </div>

        {/* Legend */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Legend</h4>
          <div className="flex flex-wrap gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-red-700"></div>
              <span className="text-gray-600">Heavy Flow</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <span className="text-gray-600">Normal Flow</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-red-300"></div>
              <span className="text-gray-600">Light Flow</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-purple-400"></div>
              <span className="text-gray-600">Symptoms</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-pink-500"></div>
              <span className="text-gray-600">Ovulation</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-pink-300"></div>
              <span className="text-gray-600">Fertile Window</span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Log Modal */}
      <AnimatePresence>
        {showQuickLogModal && quickLogDate && (
          <QuickLogModal
            date={quickLogDate}
            onClose={() => {
              setShowQuickLogModal(false);
              setQuickLogDate(null);
            }}
            onSave={() => {
              loadCalendarData(); // Refresh calendar data
              setShowQuickLogModal(false);
              setQuickLogDate(null);
            }}
          />
        )}
      </AnimatePresence>
    </>
  );
};

// =============================================
// QUICK LOG MODAL COMPONENT
// =============================================

interface QuickLogModalProps {
  date: Date;
  onClose: () => void;
  onSave: () => void;
}

const QuickLogModal: React.FC<QuickLogModalProps> = ({ date, onClose, onSave }) => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'period' | 'symptoms' | 'fertility'>('period');
  const [loading, setLoading] = useState(false);

  // Period data
  const [hasPeriod, setHasPeriod] = useState(false);
  const [flowIntensity, setFlowIntensity] = useState<number>(2);
  const [flowColor, setFlowColor] = useState<string>('bright_red');
  const [clotsPresent, setClotsPresent] = useState(false);
  const [spotting, setSpotting] = useState(false);

  // Symptoms data
  const [symptoms, setSymptoms] = useState({
    cramps: 0,
    bloating: 0,
    breast_tenderness: 0,
    headache: 0,
    back_pain: 0,
    fatigue: 0,
    mood_swings: 0,
    irritability: 0,
    anxiety: 0,
    energy_level: 5,
    mood_rating: 5
  });

  // Fertility data
  const [basalBodyTemp, setBasalBodyTemp] = useState<string>('');
  const [cervicalMucus, setCervicalMucus] = useState<string>('');
  const [ovulationTest, setOvulationTest] = useState<string>('');
  const [sexualActivity, setSexualActivity] = useState(false);

  useEffect(() => {
    loadExistingData();
  }, [date, user]);

  const loadExistingData = async () => {
    try {
      const dateStr = format(date, 'yyyy-MM-dd');
      const [periodLogs, symptomLogs, fertilityLogs] = await Promise.all([
        womensHealthService.getPeriodLogs(user!.id, dateStr, dateStr),
        womensHealthService.getSymptomLogs(user!.id, dateStr, dateStr),
        womensHealthService.getFertilityLogs(user!.id, dateStr, dateStr)
      ]);

      // Load existing period data
      if (periodLogs[0]) {
        const log = periodLogs[0];
        setHasPeriod(log.has_period);
        setFlowIntensity(log.flow_intensity || 2);
        setFlowColor(log.flow_color || 'bright_red');
        setClotsPresent(log.clots_present);
        setSpotting(log.spotting);
      }

      // Load existing symptoms
      if (symptomLogs[0]) {
        const log = symptomLogs[0];
        setSymptoms({
          cramps: log.cramps || 0,
          bloating: log.bloating || 0,
          breast_tenderness: log.breast_tenderness || 0,
          headache: log.headache || 0,
          back_pain: log.back_pain || 0,
          fatigue: log.fatigue || 0,
          mood_swings: log.mood_swings || 0,
          irritability: log.irritability || 0,
          anxiety: log.anxiety || 0,
          energy_level: log.energy_level || 5,
          mood_rating: log.mood_rating || 5
        });
      }

      // Load existing fertility data
      if (fertilityLogs[0]) {
        const log = fertilityLogs[0];
        setBasalBodyTemp(log.basal_body_temperature?.toString() || '');
        setCervicalMucus(log.cervical_mucus || '');
        setOvulationTest(log.ovulation_test_result || '');
        setSexualActivity(log.sexual_activity);
      }
    } catch (error) {
      console.error('Error loading existing data:', error);
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      const dateStr = format(date, 'yyyy-MM-dd');

      // Save period data
      if (activeTab === 'period' || hasPeriod) {
        await womensHealthService.logPeriodDay(user!.id, {
          log_date: dateStr,
          has_period: hasPeriod,
          flow_intensity: hasPeriod ? flowIntensity : undefined,
          flow_color: hasPeriod ? flowColor as any : undefined,
          clots_present: clotsPresent,
          spotting: spotting
        });
      }

      // Save symptoms data
      if (activeTab === 'symptoms' || Object.values(symptoms).some(v => v > 0)) {
        const symptomsToSave = Object.fromEntries(
          Object.entries(symptoms).filter(([_, value]) => value > 0)
        );

        if (Object.keys(symptomsToSave).length > 0) {
          await womensHealthService.logSymptoms(user!.id, {
            log_date: dateStr,
            ...symptomsToSave
          });
        }
      }

      // Save fertility data
      if (activeTab === 'fertility' || basalBodyTemp || cervicalMucus || ovulationTest || sexualActivity) {
        await womensHealthService.logFertilityData(user!.id, {
          log_date: dateStr,
          basal_body_temperature: basalBodyTemp ? parseFloat(basalBodyTemp) : undefined,
          cervical_mucus: cervicalMucus as any || undefined,
          ovulation_test_result: ovulationTest as any || undefined,
          sexual_activity: sexualActivity,
          protected_intercourse: false // Default for now
        });
      }

      onSave();
    } catch (error) {
      console.error('Error saving data:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-pink-50">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Log Data - {format(date, 'MMM d, yyyy')}
            </h3>
            <button
              onClick={onClose}
              className="p-1 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          {/* Tab Navigation */}
          <div className="flex space-x-1 mt-4">
            {[
              { id: 'period', label: 'Period', icon: Droplets },
              { id: 'symptoms', label: 'Symptoms', icon: Activity },
              { id: 'fertility', label: 'Fertility', icon: Heart }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Modal Content */}
        <div className="p-6 max-h-96 overflow-y-auto">
          {activeTab === 'period' && (
            <PeriodLogForm
              hasPeriod={hasPeriod}
              setHasPeriod={setHasPeriod}
              flowIntensity={flowIntensity}
              setFlowIntensity={setFlowIntensity}
              flowColor={flowColor}
              setFlowColor={setFlowColor}
              clotsPresent={clotsPresent}
              setClotsPresent={setClotsPresent}
              spotting={spotting}
              setSpotting={setSpotting}
            />
          )}

          {activeTab === 'symptoms' && (
            <SymptomsLogForm
              symptoms={symptoms}
              setSymptoms={setSymptoms}
            />
          )}

          {activeTab === 'fertility' && (
            <FertilityLogForm
              basalBodyTemp={basalBodyTemp}
              setBasalBodyTemp={setBasalBodyTemp}
              cervicalMucus={cervicalMucus}
              setCervicalMucus={setCervicalMucus}
              ovulationTest={ovulationTest}
              setOvulationTest={setOvulationTest}
              sexualActivity={sexualActivity}
              setSexualActivity={setSexualActivity}
            />
          )}
        </div>

        {/* Modal Footer */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={loading}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
            >
              {loading && <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>}
              <span>Save</span>
            </button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

// =============================================
// FORM COMPONENTS
// =============================================

interface PeriodLogFormProps {
  hasPeriod: boolean;
  setHasPeriod: (value: boolean) => void;
  flowIntensity: number;
  setFlowIntensity: (value: number) => void;
  flowColor: string;
  setFlowColor: (value: string) => void;
  clotsPresent: boolean;
  setClotsPresent: (value: boolean) => void;
  spotting: boolean;
  setSpotting: (value: boolean) => void;
}

const PeriodLogForm: React.FC<PeriodLogFormProps> = ({
  hasPeriod,
  setHasPeriod,
  flowIntensity,
  setFlowIntensity,
  flowColor,
  setFlowColor,
  clotsPresent,
  setClotsPresent,
  spotting,
  setSpotting
}) => {
  return (
    <div className="space-y-4">
      {/* Has Period Toggle */}
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-gray-700">Period today?</label>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={hasPeriod}
            onChange={(e) => setHasPeriod(e.target.checked)}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
        </label>
      </div>

      {hasPeriod && (
        <>
          {/* Flow Intensity */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Flow Intensity</label>
            <div className="flex space-x-2">
              {[
                { value: 1, label: 'Light', color: 'bg-red-300' },
                { value: 2, label: 'Normal', color: 'bg-red-500' },
                { value: 3, label: 'Heavy', color: 'bg-red-700' }
              ].map((option) => (
                <button
                  key={option.value}
                  onClick={() => setFlowIntensity(option.value)}
                  className={`flex-1 p-3 rounded-lg border-2 transition-all ${
                    flowIntensity === option.value
                      ? 'border-red-500 bg-red-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className={`w-4 h-4 rounded-full ${option.color} mx-auto mb-1`}></div>
                  <div className="text-xs font-medium text-gray-700">{option.label}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Flow Color */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Flow Color</label>
            <select
              value={flowColor}
              onChange={(e) => setFlowColor(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
            >
              <option value="bright_red">Bright Red</option>
              <option value="dark_red">Dark Red</option>
              <option value="brown">Brown</option>
              <option value="pink">Pink</option>
              <option value="orange">Orange</option>
            </select>
          </div>

          {/* Additional Options */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Clots present?</label>
              <input
                type="checkbox"
                checked={clotsPresent}
                onChange={(e) => setClotsPresent(e.target.checked)}
                className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500"
              />
            </div>
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">Spotting?</label>
              <input
                type="checkbox"
                checked={spotting}
                onChange={(e) => setSpotting(e.target.checked)}
                className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500"
              />
            </div>
          </div>
        </>
      )}
    </div>
  );
};

interface SymptomsLogFormProps {
  symptoms: any;
  setSymptoms: (symptoms: any) => void;
}

const SymptomsLogForm: React.FC<SymptomsLogFormProps> = ({ symptoms, setSymptoms }) => {
  const updateSymptom = (key: string, value: number) => {
    setSymptoms(prev => ({ ...prev, [key]: value }));
  };

  const symptomCategories = [
    {
      title: 'Physical Symptoms',
      symptoms: [
        { key: 'cramps', label: 'Cramps', max: 5 },
        { key: 'bloating', label: 'Bloating', max: 5 },
        { key: 'breast_tenderness', label: 'Breast Tenderness', max: 5 },
        { key: 'headache', label: 'Headache', max: 5 },
        { key: 'back_pain', label: 'Back Pain', max: 5 },
        { key: 'fatigue', label: 'Fatigue', max: 5 }
      ]
    },
    {
      title: 'Emotional Symptoms',
      symptoms: [
        { key: 'mood_swings', label: 'Mood Swings', max: 5 },
        { key: 'irritability', label: 'Irritability', max: 5 },
        { key: 'anxiety', label: 'Anxiety', max: 5 }
      ]
    },
    {
      title: 'Overall Ratings',
      symptoms: [
        { key: 'energy_level', label: 'Energy Level', max: 10 },
        { key: 'mood_rating', label: 'Mood Rating', max: 10 }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {symptomCategories.map((category) => (
        <div key={category.title}>
          <h4 className="text-sm font-medium text-gray-900 mb-3">{category.title}</h4>
          <div className="space-y-3">
            {category.symptoms.map((symptom) => (
              <div key={symptom.key}>
                <div className="flex items-center justify-between mb-1">
                  <label className="text-sm text-gray-700">{symptom.label}</label>
                  <span className="text-sm font-medium text-gray-900">
                    {symptoms[symptom.key]}/{symptom.max}
                  </span>
                </div>
                <input
                  type="range"
                  min="0"
                  max={symptom.max}
                  value={symptoms[symptom.key]}
                  onChange={(e) => updateSymptom(symptom.key, parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                />
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

interface FertilityLogFormProps {
  basalBodyTemp: string;
  setBasalBodyTemp: (value: string) => void;
  cervicalMucus: string;
  setCervicalMucus: (value: string) => void;
  ovulationTest: string;
  setOvulationTest: (value: string) => void;
  sexualActivity: boolean;
  setSexualActivity: (value: boolean) => void;
}

const FertilityLogForm: React.FC<FertilityLogFormProps> = ({
  basalBodyTemp,
  setBasalBodyTemp,
  cervicalMucus,
  setCervicalMucus,
  ovulationTest,
  setOvulationTest,
  sexualActivity,
  setSexualActivity
}) => {
  return (
    <div className="space-y-4">
      {/* Basal Body Temperature */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Basal Body Temperature (°C)
        </label>
        <input
          type="number"
          step="0.01"
          min="35"
          max="40"
          value={basalBodyTemp}
          onChange={(e) => setBasalBodyTemp(e.target.value)}
          placeholder="36.50"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
        />
      </div>

      {/* Cervical Mucus */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Cervical Mucus</label>
        <select
          value={cervicalMucus}
          onChange={(e) => setCervicalMucus(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
        >
          <option value="">Select...</option>
          <option value="dry">Dry</option>
          <option value="sticky">Sticky</option>
          <option value="creamy">Creamy</option>
          <option value="watery">Watery</option>
          <option value="egg_white">Egg White</option>
        </select>
      </div>

      {/* Ovulation Test */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Ovulation Test</label>
        <select
          value={ovulationTest}
          onChange={(e) => setOvulationTest(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
        >
          <option value="">Not taken</option>
          <option value="negative">Negative</option>
          <option value="positive">Positive</option>
          <option value="peak">Peak</option>
        </select>
      </div>

      {/* Sexual Activity */}
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-gray-700">Sexual Activity</label>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={sexualActivity}
            onChange={(e) => setSexualActivity(e.target.checked)}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-pink-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pink-500"></div>
        </label>
      </div>
    </div>
  );
};

export default MenstrualCalendar;