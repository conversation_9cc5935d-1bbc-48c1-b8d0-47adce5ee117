/**
 * Women's Health Service
 * Comprehensive service for menstrual cycle tracking, fertility monitoring, and reproductive health
 */

import { supabase } from './supabaseClient';

// =============================================
// TYPE DEFINITIONS
// =============================================

export interface MenstrualCycle {
  id: string;
  user_id: string;
  cycle_number: number;
  cycle_start_date: string;
  cycle_end_date?: string;
  period_start_date: string;
  period_end_date?: string;
  cycle_length?: number;
  period_length?: number;
  average_flow_intensity?: number;
  is_current_cycle: boolean;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface DailyPeriodLog {
  id: string;
  user_id: string;
  cycle_id?: string;
  log_date: string;
  has_period: boolean;
  flow_intensity?: number; // 1=light, 2=normal, 3=heavy
  flow_color?: 'bright_red' | 'dark_red' | 'brown' | 'pink' | 'orange';
  clots_present: boolean;
  spotting: boolean;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface DailySymptoms {
  id: string;
  user_id: string;
  cycle_id?: string;
  log_date: string;
  // Physical symptoms (1-5 scale)
  cramps?: number;
  bloating?: number;
  breast_tenderness?: number;
  headache?: number;
  back_pain?: number;
  fatigue?: number;
  nausea?: number;
  acne?: number;
  hot_flashes?: number;
  // Emotional symptoms (1-5 scale)
  mood_swings?: number;
  irritability?: number;
  anxiety?: number;
  depression?: number;
  emotional_sensitivity?: number;
  // Overall ratings (1-10 scale)
  energy_level?: number;
  mood_rating?: number;
  sleep_quality?: number;
  other_symptoms?: Record<string, any>;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface FertilityTracking {
  id: string;
  user_id: string;
  cycle_id?: string;
  log_date: string;
  basal_body_temperature?: number;
  cervical_mucus?: 'dry' | 'sticky' | 'creamy' | 'watery' | 'egg_white';
  cervical_position?: 'low_firm_closed' | 'medium' | 'high_soft_open';
  ovulation_test_result?: 'negative' | 'positive' | 'peak';
  ovulation_pain: boolean;
  ovulation_pain_intensity?: number;
  sexual_activity: boolean;
  protected_intercourse: boolean;
  contraception_method?: string;
  predicted_ovulation: boolean;
  fertile_window: boolean;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface CyclePrediction {
  id: string;
  user_id: string;
  prediction_date: string;
  next_period_start?: string;
  next_period_end?: string;
  next_ovulation_date?: string;
  fertile_window_start?: string;
  fertile_window_end?: string;
  next_cycle_length?: number;
  period_confidence: number;
  ovulation_confidence: number;
  algorithm_version: string;
  created_at: string;
}

export interface WomensHealthSettings {
  id: string;
  user_id: string;
  average_cycle_length: number;
  average_period_length: number;
  luteal_phase_length: number;
  track_symptoms: boolean;
  track_fertility: boolean;
  track_mood: boolean;
  track_sexual_activity: boolean;
  period_reminders: boolean;
  ovulation_reminders: boolean;
  symptom_reminders: boolean;
  trying_to_conceive: boolean;
  avoiding_pregnancy: boolean;
  tracking_for_health: boolean;
  data_sharing_enabled: boolean;
  anonymous_mode: boolean;
  created_at: string;
  updated_at: string;
}

export interface CyclePhase {
  phase: 'menstrual' | 'follicular' | 'ovulation' | 'luteal' | 'unknown';
  day: number;
  description: string;
  tips: string[];
}

export interface CycleInsights {
  current_phase: CyclePhase;
  days_until_next_period?: number;
  days_until_ovulation?: number;
  fertility_status: 'high' | 'medium' | 'low';
  cycle_regularity: 'regular' | 'irregular' | 'unknown';
  average_cycle_length: number;
  symptom_patterns: Record<string, number>;
  recommendations: string[];
}

// =============================================
// CORE SERVICE CLASS
// =============================================

class WomensHealthService {

  // =============================================
  // CYCLE MANAGEMENT
  // =============================================

  async getCurrentCycle(userId: string): Promise<MenstrualCycle | null> {
    const { data, error } = await supabase
      .from('menstrual_cycles')
      .select('*')
      .eq('user_id', userId)
      .eq('is_current_cycle', true)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    return data;
  }

  async getCycleHistory(userId: string, limit: number = 12): Promise<MenstrualCycle[]> {
    const { data, error } = await supabase
      .from('menstrual_cycles')
      .select('*')
      .eq('user_id', userId)
      .order('cycle_start_date', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  async startNewCycle(userId: string, periodStartDate: string, notes?: string): Promise<MenstrualCycle> {
    // End current cycle if exists
    const currentCycle = await this.getCurrentCycle(userId);
    if (currentCycle) {
      await this.endCycle(userId, new Date(periodStartDate).toISOString().split('T')[0]);
    }

    // Get next cycle number
    const { data: lastCycle } = await supabase
      .from('menstrual_cycles')
      .select('cycle_number')
      .eq('user_id', userId)
      .order('cycle_number', { ascending: false })
      .limit(1)
      .single();

    const cycleNumber = (lastCycle?.cycle_number || 0) + 1;

    // Create new cycle
    const { data, error } = await supabase
      .from('menstrual_cycles')
      .insert({
        user_id: userId,
        cycle_number: cycleNumber,
        cycle_start_date: periodStartDate,
        period_start_date: periodStartDate,
        is_current_cycle: true,
        notes
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async endCycle(userId: string, endDate: string): Promise<void> {
    const { error } = await supabase
      .from('menstrual_cycles')
      .update({
        cycle_end_date: endDate,
        is_current_cycle: false,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('is_current_cycle', true);

    if (error) throw error;
  }

  async endPeriod(userId: string, endDate: string): Promise<void> {
    const { error } = await supabase
      .from('menstrual_cycles')
      .update({
        period_end_date: endDate,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('is_current_cycle', true);

    if (error) throw error;
  }

  // =============================================
  // DAILY LOGGING
  // =============================================

  async logPeriodDay(userId: string, logData: Partial<DailyPeriodLog>): Promise<DailyPeriodLog> {
    const currentCycle = await this.getCurrentCycle(userId);

    const { data, error } = await supabase
      .from('daily_period_logs')
      .upsert({
        user_id: userId,
        cycle_id: currentCycle?.id,
        ...logData,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async logSymptoms(userId: string, logData: Partial<DailySymptoms>): Promise<DailySymptoms> {
    const currentCycle = await this.getCurrentCycle(userId);

    const { data, error } = await supabase
      .from('daily_symptoms')
      .upsert({
        user_id: userId,
        cycle_id: currentCycle?.id,
        ...logData,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async logFertilityData(userId: string, logData: Partial<FertilityTracking>): Promise<FertilityTracking> {
    const currentCycle = await this.getCurrentCycle(userId);

    const { data, error } = await supabase
      .from('fertility_tracking')
      .upsert({
        user_id: userId,
        cycle_id: currentCycle?.id,
        ...logData,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // =============================================
  // DATA RETRIEVAL
  // =============================================

  async getPeriodLogs(userId: string, startDate: string, endDate: string): Promise<DailyPeriodLog[]> {
    const { data, error } = await supabase
      .from('daily_period_logs')
      .select('*')
      .eq('user_id', userId)
      .gte('log_date', startDate)
      .lte('log_date', endDate)
      .order('log_date', { ascending: true });

    if (error) throw error;
    return data || [];
  }

  async getSymptomLogs(userId: string, startDate: string, endDate: string): Promise<DailySymptoms[]> {
    const { data, error } = await supabase
      .from('daily_symptoms')
      .select('*')
      .eq('user_id', userId)
      .gte('log_date', startDate)
      .lte('log_date', endDate)
      .order('log_date', { ascending: true });

    if (error) throw error;
    return data || [];
  }

  async getFertilityLogs(userId: string, startDate: string, endDate: string): Promise<FertilityTracking[]> {
    const { data, error } = await supabase
      .from('fertility_tracking')
      .select('*')
      .eq('user_id', userId)
      .gte('log_date', startDate)
      .lte('log_date', endDate)
      .order('log_date', { ascending: true });

    if (error) throw error;
    return data || [];
  }

  // =============================================
  // PREDICTION ALGORITHMS
  // =============================================

  async calculateCyclePredictions(userId: string): Promise<CyclePrediction> {
    const settings = await this.getUserSettings(userId);
    const cycleHistory = await this.getCycleHistory(userId, 6);
    const currentCycle = await this.getCurrentCycle(userId);

    if (!currentCycle) {
      throw new Error('No current cycle found');
    }

    // Calculate average cycle length from history
    const completedCycles = cycleHistory.filter(c => c.cycle_length);
    let avgCycleLength = settings.average_cycle_length;
    let periodConfidence = 0.5;
    let ovulationConfidence = 0.5;

    if (completedCycles.length >= 3) {
      const cycleLengths = completedCycles.map(c => c.cycle_length!);
      avgCycleLength = Math.round(cycleLengths.reduce((a, b) => a + b, 0) / cycleLengths.length);

      // Calculate confidence based on cycle regularity
      const variance = this.calculateVariance(cycleLengths);
      periodConfidence = Math.max(0.3, Math.min(0.95, 1 - (variance / 100)));
      ovulationConfidence = periodConfidence * 0.8; // Ovulation is less predictable
    }

    // Predict next period
    const currentCycleStart = new Date(currentCycle.cycle_start_date);
    const nextPeriodStart = new Date(currentCycleStart);
    nextPeriodStart.setDate(nextPeriodStart.getDate() + avgCycleLength);

    const nextPeriodEnd = new Date(nextPeriodStart);
    nextPeriodEnd.setDate(nextPeriodEnd.getDate() + settings.average_period_length - 1);

    // Predict ovulation (luteal phase length before next period)
    const nextOvulation = new Date(nextPeriodStart);
    nextOvulation.setDate(nextOvulation.getDate() - settings.luteal_phase_length);

    // Fertile window (5 days before ovulation + ovulation day + 1 day after)
    const fertileStart = new Date(nextOvulation);
    fertileStart.setDate(fertileStart.getDate() - 5);

    const fertileEnd = new Date(nextOvulation);
    fertileEnd.setDate(fertileEnd.getDate() + 1);

    const prediction: Omit<CyclePrediction, 'id' | 'created_at'> = {
      user_id: userId,
      prediction_date: new Date().toISOString().split('T')[0],
      next_period_start: nextPeriodStart.toISOString().split('T')[0],
      next_period_end: nextPeriodEnd.toISOString().split('T')[0],
      next_ovulation_date: nextOvulation.toISOString().split('T')[0],
      fertile_window_start: fertileStart.toISOString().split('T')[0],
      fertile_window_end: fertileEnd.toISOString().split('T')[0],
      next_cycle_length: avgCycleLength,
      period_confidence: Math.round(periodConfidence * 100) / 100,
      ovulation_confidence: Math.round(ovulationConfidence * 100) / 100,
      algorithm_version: '1.0'
    };

    // Save prediction
    const { data, error } = await supabase
      .from('cycle_predictions')
      .upsert(prediction)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  private calculateVariance(numbers: number[]): number {
    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
    const squaredDiffs = numbers.map(n => Math.pow(n - mean, 2));
    return squaredDiffs.reduce((a, b) => a + b, 0) / numbers.length;
  }

  async getCurrentCycleInsights(userId: string): Promise<CycleInsights> {
    const currentCycle = await this.getCurrentCycle(userId);
    const settings = await this.getUserSettings(userId);
    const predictions = await this.getLatestPredictions(userId);

    if (!currentCycle) {
      throw new Error('No current cycle found');
    }

    const today = new Date();
    const cycleStart = new Date(currentCycle.cycle_start_date);
    const cycleDay = Math.floor((today.getTime() - cycleStart.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    // Determine current phase
    const currentPhase = this.calculateCyclePhase(cycleDay, settings);

    // Calculate days until next events
    let daysUntilPeriod: number | undefined;
    let daysUntilOvulation: number | undefined;

    if (predictions) {
      const nextPeriod = new Date(predictions.next_period_start!);
      const nextOvulation = new Date(predictions.next_ovulation_date!);

      daysUntilPeriod = Math.ceil((nextPeriod.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      daysUntilOvulation = Math.ceil((nextOvulation.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    }

    // Determine fertility status
    let fertilityStatus: 'high' | 'medium' | 'low' = 'low';
    if (predictions && daysUntilOvulation !== undefined) {
      if (daysUntilOvulation >= -1 && daysUntilOvulation <= 1) {
        fertilityStatus = 'high';
      } else if (daysUntilOvulation >= -5 && daysUntilOvulation <= 2) {
        fertilityStatus = 'medium';
      }
    }

    // Calculate cycle regularity
    const cycleHistory = await this.getCycleHistory(userId, 6);
    const completedCycles = cycleHistory.filter(c => c.cycle_length);
    let cycleRegularity: 'regular' | 'irregular' | 'unknown' = 'unknown';

    if (completedCycles.length >= 3) {
      const variance = this.calculateVariance(completedCycles.map(c => c.cycle_length!));
      cycleRegularity = variance <= 7 ? 'regular' : 'irregular';
    }

    // Get symptom patterns (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const symptoms = await this.getSymptomLogs(userId, thirtyDaysAgo.toISOString().split('T')[0], today.toISOString().split('T')[0]);

    const symptomPatterns = this.analyzeSymptomPatterns(symptoms);

    // Generate recommendations
    const recommendations = this.generateRecommendations(currentPhase, fertilityStatus, symptomPatterns, settings);

    return {
      current_phase: currentPhase,
      days_until_next_period: daysUntilPeriod,
      days_until_ovulation: daysUntilOvulation,
      fertility_status: fertilityStatus,
      cycle_regularity: cycleRegularity,
      average_cycle_length: settings.average_cycle_length,
      symptom_patterns: symptomPatterns,
      recommendations
    };
  }

  private calculateCyclePhase(cycleDay: number, settings: WomensHealthSettings): CyclePhase {
    const ovulationDay = settings.average_cycle_length - settings.luteal_phase_length + 1;

    if (cycleDay <= settings.average_period_length) {
      return {
        phase: 'menstrual',
        day: cycleDay,
        description: 'Menstrual phase - Your period is here',
        tips: [
          'Stay hydrated and rest when needed',
          'Use heat therapy for cramps',
          'Eat iron-rich foods to replenish nutrients',
          'Light exercise like walking can help with cramps'
        ]
      };
    } else if (cycleDay <= ovulationDay - 5) {
      return {
        phase: 'follicular',
        day: cycleDay,
        description: 'Follicular phase - Your body is preparing for ovulation',
        tips: [
          'Energy levels are rising - great time for new projects',
          'Focus on strength training and cardio',
          'Eat plenty of fresh fruits and vegetables',
          'This is a good time for social activities'
        ]
      };
    } else if (cycleDay <= ovulationDay + 1) {
      return {
        phase: 'ovulation',
        day: cycleDay,
        description: 'Ovulation phase - Peak fertility window',
        tips: [
          'Highest fertility - track cervical mucus changes',
          'Energy and mood are typically at their peak',
          'Great time for important conversations',
          'Stay hydrated and eat antioxidant-rich foods'
        ]
      };
    } else {
      return {
        phase: 'luteal',
        day: cycleDay,
        description: 'Luteal phase - Your body may be preparing for the next cycle',
        tips: [
          'PMS symptoms may appear - practice self-care',
          'Focus on gentle exercises like yoga',
          'Eat complex carbs to stabilize mood',
          'Prioritize sleep and stress management'
        ]
      };
    }
  }

  private analyzeSymptomPatterns(symptoms: DailySymptoms[]): Record<string, number> {
    if (symptoms.length === 0) return {};

    const patterns: Record<string, number[]> = {};

    symptoms.forEach(symptom => {
      if (symptom.cramps) patterns.cramps = [...(patterns.cramps || []), symptom.cramps];
      if (symptom.bloating) patterns.bloating = [...(patterns.bloating || []), symptom.bloating];
      if (symptom.mood_swings) patterns.mood_swings = [...(patterns.mood_swings || []), symptom.mood_swings];
      if (symptom.fatigue) patterns.fatigue = [...(patterns.fatigue || []), symptom.fatigue];
      if (symptom.headache) patterns.headache = [...(patterns.headache || []), symptom.headache];
      if (symptom.breast_tenderness) patterns.breast_tenderness = [...(patterns.breast_tenderness || []), symptom.breast_tenderness];
    });

    const averages: Record<string, number> = {};
    Object.keys(patterns).forEach(key => {
      const values = patterns[key];
      averages[key] = Math.round((values.reduce((a, b) => a + b, 0) / values.length) * 10) / 10;
    });

    return averages;
  }

  private generateRecommendations(
    phase: CyclePhase,
    fertilityStatus: 'high' | 'medium' | 'low',
    symptomPatterns: Record<string, number>,
    settings: WomensHealthSettings
  ): string[] {
    const recommendations: string[] = [];

    // Phase-based recommendations
    recommendations.push(...phase.tips);

    // Fertility-based recommendations
    if (settings.trying_to_conceive && fertilityStatus === 'high') {
      recommendations.push('This is your most fertile time - consider timing intercourse');
      recommendations.push('Track basal body temperature for more accurate ovulation detection');
    } else if (settings.avoiding_pregnancy && fertilityStatus === 'high') {
      recommendations.push('Use extra caution with contraception during this fertile window');
    }

    // Symptom-based recommendations
    if (symptomPatterns.cramps && symptomPatterns.cramps >= 3) {
      recommendations.push('Consider magnesium supplements for cramp relief (consult your doctor)');
      recommendations.push('Try gentle heat therapy and stretching exercises');
    }

    if (symptomPatterns.mood_swings && symptomPatterns.mood_swings >= 3) {
      recommendations.push('Practice mindfulness and stress-reduction techniques');
      recommendations.push('Maintain regular sleep schedule to help stabilize mood');
    }

    if (symptomPatterns.fatigue && symptomPatterns.fatigue >= 3) {
      recommendations.push('Ensure adequate iron intake through diet or supplements');
      recommendations.push('Prioritize 7-9 hours of quality sleep');
    }

    return recommendations.slice(0, 6); // Limit to 6 recommendations
  }

  // =============================================
  // SETTINGS MANAGEMENT
  // =============================================

  async getUserSettings(userId: string): Promise<WomensHealthSettings> {
    const { data, error } = await supabase
      .from('womens_health_settings')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code === 'PGRST116') {
      // Create default settings if none exist
      return await this.createDefaultSettings(userId);
    }

    if (error) throw error;
    return data;
  }

  async updateUserSettings(userId: string, settings: Partial<WomensHealthSettings>): Promise<WomensHealthSettings> {
    const { data, error } = await supabase
      .from('womens_health_settings')
      .upsert({
        user_id: userId,
        ...settings,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  private async createDefaultSettings(userId: string): Promise<WomensHealthSettings> {
    const defaultSettings = {
      user_id: userId,
      average_cycle_length: 28,
      average_period_length: 5,
      luteal_phase_length: 14,
      track_symptoms: true,
      track_fertility: true,
      track_mood: true,
      track_sexual_activity: false,
      period_reminders: true,
      ovulation_reminders: true,
      symptom_reminders: false,
      trying_to_conceive: false,
      avoiding_pregnancy: false,
      tracking_for_health: true,
      data_sharing_enabled: false,
      anonymous_mode: false
    };

    const { data, error } = await supabase
      .from('womens_health_settings')
      .insert(defaultSettings)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getLatestPredictions(userId: string): Promise<CyclePrediction | null> {
    const { data, error } = await supabase
      .from('cycle_predictions')
      .select('*')
      .eq('user_id', userId)
      .order('prediction_date', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    return data;
  }
}

// Export singleton instance
export const womensHealthService = new WomensHealthService();