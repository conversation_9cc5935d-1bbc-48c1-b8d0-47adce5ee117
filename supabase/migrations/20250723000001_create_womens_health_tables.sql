-- Women's Health Comprehensive Database Schema
-- Created: 2025-07-23
-- Purpose: Complete menstrual cycle, fertility, and reproductive health tracking

-- =============================================
-- MENSTRUAL CYCLES TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.menstrual_cycles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    cycle_number INTEGER NOT NULL,
    cycle_start_date DATE NOT NULL,
    cycle_end_date DATE,
    period_start_date DATE NOT NULL,
    period_end_date DATE,
    cycle_length INTEGER, -- Calculated when cycle ends
    period_length INTEGER, -- Calculated when period ends
    average_flow_intensity DECIMAL(3,2), -- 1.0 = light, 2.0 = normal, 3.0 = heavy
    is_current_cycle BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

    -- Constraints
    CONSTRAINT valid_cycle_dates CHECK (cycle_end_date IS NULL OR cycle_end_date >= cycle_start_date),
    CONSTRAINT valid_period_dates CHECK (period_end_date IS NULL OR period_end_date >= period_start_date),
    CONSTRAINT valid_cycle_length CHECK (cycle_length IS NULL OR (cycle_length >= 21 AND cycle_length <= 45)),
    CONSTRAINT valid_period_length CHECK (period_length IS NULL OR (period_length >= 1 AND period_length <= 10)),
    CONSTRAINT valid_flow_intensity CHECK (average_flow_intensity >= 1.0 AND average_flow_intensity <= 3.0)
);

-- =============================================
-- DAILY PERIOD LOGS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.daily_period_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    cycle_id UUID REFERENCES public.menstrual_cycles(id) ON DELETE CASCADE,
    log_date DATE NOT NULL,
    has_period BOOLEAN DEFAULT FALSE,
    flow_intensity INTEGER CHECK (flow_intensity >= 1 AND flow_intensity <= 3), -- 1=light, 2=normal, 3=heavy
    flow_color TEXT CHECK (flow_color IN ('bright_red', 'dark_red', 'brown', 'pink', 'orange')),
    clots_present BOOLEAN DEFAULT FALSE,
    spotting BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

    -- Unique constraint to prevent duplicate logs for same date
    UNIQUE(user_id, log_date)
);

-- =============================================
-- SYMPTOMS TRACKING TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.daily_symptoms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    cycle_id UUID REFERENCES public.menstrual_cycles(id) ON DELETE CASCADE,
    log_date DATE NOT NULL,

    -- Physical Symptoms (1-5 scale: 1=mild, 5=severe)
    cramps INTEGER CHECK (cramps >= 1 AND cramps <= 5),
    bloating INTEGER CHECK (bloating >= 1 AND bloating <= 5),
    breast_tenderness INTEGER CHECK (breast_tenderness >= 1 AND breast_tenderness <= 5),
    headache INTEGER CHECK (headache >= 1 AND headache <= 5),
    back_pain INTEGER CHECK (back_pain >= 1 AND back_pain <= 5),
    fatigue INTEGER CHECK (fatigue >= 1 AND fatigue <= 5),
    nausea INTEGER CHECK (nausea >= 1 AND nausea <= 5),
    acne INTEGER CHECK (acne >= 1 AND acne <= 5),
    hot_flashes INTEGER CHECK (hot_flashes >= 1 AND hot_flashes <= 5),

    -- Emotional Symptoms (1-5 scale)
    mood_swings INTEGER CHECK (mood_swings >= 1 AND mood_swings <= 5),
    irritability INTEGER CHECK (irritability >= 1 AND irritability <= 5),
    anxiety INTEGER CHECK (anxiety >= 1 AND anxiety <= 5),
    depression INTEGER CHECK (depression >= 1 AND depression <= 5),
    emotional_sensitivity INTEGER CHECK (emotional_sensitivity >= 1 AND emotional_sensitivity <= 5),

    -- Overall ratings (1-10 scale)
    energy_level INTEGER CHECK (energy_level >= 1 AND energy_level <= 10),
    mood_rating INTEGER CHECK (mood_rating >= 1 AND mood_rating <= 10),
    sleep_quality INTEGER CHECK (sleep_quality >= 1 AND sleep_quality <= 10),

    -- Additional symptoms as JSON for flexibility
    other_symptoms JSONB DEFAULT '{}',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

    -- Unique constraint to prevent duplicate logs for same date
    UNIQUE(user_id, log_date)
);

-- =============================================
-- FERTILITY TRACKING TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.fertility_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    cycle_id UUID REFERENCES public.menstrual_cycles(id) ON DELETE CASCADE,
    log_date DATE NOT NULL,

    -- Fertility indicators
    basal_body_temperature DECIMAL(4,2), -- In Celsius (e.g., 36.50)
    cervical_mucus TEXT CHECK (cervical_mucus IN ('dry', 'sticky', 'creamy', 'watery', 'egg_white')),
    cervical_position TEXT CHECK (cervical_position IN ('low_firm_closed', 'medium', 'high_soft_open')),
    ovulation_test_result TEXT CHECK (ovulation_test_result IN ('negative', 'positive', 'peak')),
    ovulation_pain BOOLEAN DEFAULT FALSE,
    ovulation_pain_intensity INTEGER CHECK (ovulation_pain_intensity >= 1 AND ovulation_pain_intensity <= 5),

    -- Sexual activity
    sexual_activity BOOLEAN DEFAULT FALSE,
    protected_intercourse BOOLEAN DEFAULT FALSE,
    contraception_method TEXT,

    -- Predictions and calculations
    predicted_ovulation BOOLEAN DEFAULT FALSE,
    fertile_window BOOLEAN DEFAULT FALSE,

    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

    -- Unique constraint to prevent duplicate logs for same date
    UNIQUE(user_id, log_date)
);

-- =============================================
-- CYCLE PREDICTIONS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.cycle_predictions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    prediction_date DATE NOT NULL,

    -- Predictions
    next_period_start DATE,
    next_period_end DATE,
    next_ovulation_date DATE,
    fertile_window_start DATE,
    fertile_window_end DATE,
    next_cycle_length INTEGER,

    -- Confidence scores (0.0 to 1.0)
    period_confidence DECIMAL(3,2) DEFAULT 0.5,
    ovulation_confidence DECIMAL(3,2) DEFAULT 0.5,

    -- Algorithm version for tracking improvements
    algorithm_version VARCHAR(10) DEFAULT '1.0',

    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

    -- Unique constraint for user and prediction date
    UNIQUE(user_id, prediction_date)
);

-- =============================================
-- WOMEN'S HEALTH SETTINGS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.womens_health_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

    -- User preferences
    average_cycle_length INTEGER DEFAULT 28 CHECK (average_cycle_length >= 21 AND average_cycle_length <= 45),
    average_period_length INTEGER DEFAULT 5 CHECK (average_period_length >= 1 AND average_period_length <= 10),
    luteal_phase_length INTEGER DEFAULT 14 CHECK (luteal_phase_length >= 10 AND luteal_phase_length <= 16),

    -- Tracking preferences
    track_symptoms BOOLEAN DEFAULT TRUE,
    track_fertility BOOLEAN DEFAULT TRUE,
    track_mood BOOLEAN DEFAULT TRUE,
    track_sexual_activity BOOLEAN DEFAULT FALSE,

    -- Notification preferences
    period_reminders BOOLEAN DEFAULT TRUE,
    ovulation_reminders BOOLEAN DEFAULT TRUE,
    symptom_reminders BOOLEAN DEFAULT FALSE,

    -- Health goals
    trying_to_conceive BOOLEAN DEFAULT FALSE,
    avoiding_pregnancy BOOLEAN DEFAULT FALSE,
    tracking_for_health BOOLEAN DEFAULT TRUE,

    -- Privacy settings
    data_sharing_enabled BOOLEAN DEFAULT FALSE,
    anonymous_mode BOOLEAN DEFAULT FALSE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

    -- Unique constraint for user
    UNIQUE(user_id)
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================
CREATE INDEX IF NOT EXISTS idx_menstrual_cycles_user_date ON public.menstrual_cycles(user_id, cycle_start_date DESC);
CREATE INDEX IF NOT EXISTS idx_menstrual_cycles_current ON public.menstrual_cycles(user_id, is_current_cycle) WHERE is_current_cycle = TRUE;
CREATE INDEX IF NOT EXISTS idx_daily_period_logs_user_date ON public.daily_period_logs(user_id, log_date DESC);
CREATE INDEX IF NOT EXISTS idx_daily_symptoms_user_date ON public.daily_symptoms(user_id, log_date DESC);
CREATE INDEX IF NOT EXISTS idx_fertility_tracking_user_date ON public.fertility_tracking(user_id, log_date DESC);
CREATE INDEX IF NOT EXISTS idx_cycle_predictions_user_date ON public.cycle_predictions(user_id, prediction_date DESC);

-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================
ALTER TABLE public.menstrual_cycles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_period_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_symptoms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fertility_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cycle_predictions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.womens_health_settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can manage their own menstrual cycles" ON public.menstrual_cycles;
DROP POLICY IF EXISTS "Users can manage their own period logs" ON public.daily_period_logs;
DROP POLICY IF EXISTS "Users can manage their own symptoms" ON public.daily_symptoms;
DROP POLICY IF EXISTS "Users can manage their own fertility data" ON public.fertility_tracking;
DROP POLICY IF EXISTS "Users can manage their own predictions" ON public.cycle_predictions;
DROP POLICY IF EXISTS "Users can manage their own settings" ON public.womens_health_settings;

-- Create RLS policies
CREATE POLICY "Users can manage their own menstrual cycles"
    ON public.menstrual_cycles
    FOR ALL
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own period logs"
    ON public.daily_period_logs
    FOR ALL
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own symptoms"
    ON public.daily_symptoms
    FOR ALL
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own fertility data"
    ON public.fertility_tracking
    FOR ALL
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own predictions"
    ON public.cycle_predictions
    FOR ALL
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own settings"
    ON public.womens_health_settings
    FOR ALL
    TO authenticated
    USING (auth.uid() = user_id);

-- =============================================
-- HELPER FUNCTIONS
-- =============================================

-- Function to calculate cycle day
CREATE OR REPLACE FUNCTION calculate_cycle_day(
    p_user_id UUID,
    p_date DATE
) RETURNS INTEGER AS $$
DECLARE
    current_cycle_start DATE;
    cycle_day INTEGER;
BEGIN
    -- Get the current cycle start date
    SELECT cycle_start_date INTO current_cycle_start
    FROM public.menstrual_cycles
    WHERE user_id = p_user_id
    AND is_current_cycle = TRUE
    LIMIT 1;

    IF current_cycle_start IS NULL THEN
        RETURN NULL;
    END IF;

    -- Calculate cycle day (1-based)
    cycle_day := (p_date - current_cycle_start) + 1;

    RETURN cycle_day;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get cycle phase
CREATE OR REPLACE FUNCTION get_cycle_phase(
    p_user_id UUID,
    p_date DATE
) RETURNS TEXT AS $$
DECLARE
    cycle_day INTEGER;
    settings RECORD;
    period_end_date DATE;
    ovulation_day INTEGER;
    phase TEXT;
BEGIN
    -- Get cycle day
    cycle_day := calculate_cycle_day(p_user_id, p_date);

    IF cycle_day IS NULL THEN
        RETURN 'unknown';
    END IF;

    -- Get user settings
    SELECT * INTO settings
    FROM public.womens_health_settings
    WHERE user_id = p_user_id;

    IF NOT FOUND THEN
        -- Use default values
        settings.average_period_length := 5;
        settings.luteal_phase_length := 14;
        settings.average_cycle_length := 28;
    END IF;

    -- Calculate ovulation day (luteal phase length before next period)
    ovulation_day := settings.average_cycle_length - settings.luteal_phase_length + 1;

    -- Determine phase
    IF cycle_day <= settings.average_period_length THEN
        phase := 'menstrual';
    ELSIF cycle_day <= (ovulation_day - 5) THEN
        phase := 'follicular';
    ELSIF cycle_day <= (ovulation_day + 1) THEN
        phase := 'ovulation';
    ELSE
        phase := 'luteal';
    END IF;

    RETURN phase;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update cycle calculations
CREATE OR REPLACE FUNCTION update_cycle_calculations()
RETURNS TRIGGER AS $$
BEGIN
    -- Update cycle length when cycle ends
    IF NEW.cycle_end_date IS NOT NULL AND OLD.cycle_end_date IS NULL THEN
        NEW.cycle_length := NEW.cycle_end_date - NEW.cycle_start_date + 1;
    END IF;

    -- Update period length when period ends
    IF NEW.period_end_date IS NOT NULL AND OLD.period_end_date IS NULL THEN
        NEW.period_length := NEW.period_end_date - NEW.period_start_date + 1;
    END IF;

    -- Update timestamp
    NEW.updated_at := now();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for cycle calculations
DROP TRIGGER IF EXISTS trigger_update_cycle_calculations ON public.menstrual_cycles;
CREATE TRIGGER trigger_update_cycle_calculations
    BEFORE UPDATE ON public.menstrual_cycles
    FOR EACH ROW
    EXECUTE FUNCTION update_cycle_calculations();

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================
CREATE INDEX IF NOT EXISTS idx_menstrual_cycles_user_date ON public.menstrual_cycles(user_id, cycle_start_date DESC);
CREATE INDEX IF NOT EXISTS idx_menstrual_cycles_current ON public.menstrual_cycles(user_id, is_current_cycle) WHERE is_current_cycle = TRUE;
CREATE INDEX IF NOT EXISTS idx_daily_period_logs_user_date ON public.daily_period_logs(user_id, log_date DESC);
CREATE INDEX IF NOT EXISTS idx_daily_symptoms_user_date ON public.daily_symptoms(user_id, log_date DESC);
CREATE INDEX IF NOT EXISTS idx_fertility_tracking_user_date ON public.fertility_tracking(user_id, log_date DESC);
CREATE INDEX IF NOT EXISTS idx_cycle_predictions_user_date ON public.cycle_predictions(user_id, prediction_date DESC);

-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================
ALTER TABLE public.menstrual_cycles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_period_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_symptoms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fertility_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cycle_predictions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.womens_health_settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can manage their own menstrual cycles" ON public.menstrual_cycles;
DROP POLICY IF EXISTS "Users can manage their own period logs" ON public.daily_period_logs;
DROP POLICY IF EXISTS "Users can manage their own symptoms" ON public.daily_symptoms;
DROP POLICY IF EXISTS "Users can manage their own fertility data" ON public.fertility_tracking;
DROP POLICY IF EXISTS "Users can manage their own predictions" ON public.cycle_predictions;
DROP POLICY IF EXISTS "Users can manage their own settings" ON public.womens_health_settings;

-- Create RLS policies
CREATE POLICY "Users can manage their own menstrual cycles"
    ON public.menstrual_cycles
    FOR ALL
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own period logs"
    ON public.daily_period_logs
    FOR ALL
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own symptoms"
    ON public.daily_symptoms
    FOR ALL
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own fertility data"
    ON public.fertility_tracking
    FOR ALL
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own predictions"
    ON public.cycle_predictions
    FOR ALL
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own settings"
    ON public.womens_health_settings
    FOR ALL
    TO authenticated
    USING (auth.uid() = user_id);

-- =============================================
-- HELPER FUNCTIONS
-- =============================================

-- Function to calculate cycle day
CREATE OR REPLACE FUNCTION calculate_cycle_day(
    p_user_id UUID,
    p_date DATE
) RETURNS INTEGER AS $$
DECLARE
    current_cycle_start DATE;
    cycle_day INTEGER;
BEGIN
    -- Get the current cycle start date
    SELECT cycle_start_date INTO current_cycle_start
    FROM public.menstrual_cycles
    WHERE user_id = p_user_id
    AND is_current_cycle = TRUE
    LIMIT 1;

    IF current_cycle_start IS NULL THEN
        RETURN NULL;
    END IF;

    -- Calculate cycle day (1-based)
    cycle_day := (p_date - current_cycle_start) + 1;

    RETURN cycle_day;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get cycle phase
CREATE OR REPLACE FUNCTION get_cycle_phase(
    p_user_id UUID,
    p_date DATE
) RETURNS TEXT AS $$
DECLARE
    cycle_day INTEGER;
    settings RECORD;
    period_end_date DATE;
    ovulation_day INTEGER;
    phase TEXT;
BEGIN
    -- Get cycle day
    cycle_day := calculate_cycle_day(p_user_id, p_date);

    IF cycle_day IS NULL THEN
        RETURN 'unknown';
    END IF;

    -- Get user settings
    SELECT * INTO settings
    FROM public.womens_health_settings
    WHERE user_id = p_user_id;

    IF NOT FOUND THEN
        -- Use default values
        settings.average_period_length := 5;
        settings.luteal_phase_length := 14;
        settings.average_cycle_length := 28;
    END IF;

    -- Calculate ovulation day (luteal phase length before next period)
    ovulation_day := settings.average_cycle_length - settings.luteal_phase_length + 1;

    -- Determine phase
    IF cycle_day <= settings.average_period_length THEN
        phase := 'menstrual';
    ELSIF cycle_day <= (ovulation_day - 5) THEN
        phase := 'follicular';
    ELSIF cycle_day <= (ovulation_day + 1) THEN
        phase := 'ovulation';
    ELSE
        phase := 'luteal';
    END IF;

    RETURN phase;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update cycle calculations
CREATE OR REPLACE FUNCTION update_cycle_calculations()
RETURNS TRIGGER AS $$
BEGIN
    -- Update cycle length when cycle ends
    IF NEW.cycle_end_date IS NOT NULL AND OLD.cycle_end_date IS NULL THEN
        NEW.cycle_length := NEW.cycle_end_date - NEW.cycle_start_date + 1;
    END IF;

    -- Update period length when period ends
    IF NEW.period_end_date IS NOT NULL AND OLD.period_end_date IS NULL THEN
        NEW.period_length := NEW.period_end_date - NEW.period_start_date + 1;
    END IF;

    -- Update timestamp
    NEW.updated_at := now();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for cycle calculations
DROP TRIGGER IF EXISTS trigger_update_cycle_calculations ON public.menstrual_cycles;
CREATE TRIGGER trigger_update_cycle_calculations
    BEFORE UPDATE ON public.menstrual_cycles
    FOR EACH ROW
    EXECUTE FUNCTION update_cycle_calculations();